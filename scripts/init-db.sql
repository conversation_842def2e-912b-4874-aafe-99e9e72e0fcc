-- Database initialization script
-- Run this to create the required tables with proper foreign key constraints

-- Create Product table
CREATE TABLE IF NOT EXISTS "Product" (
  id VARCHAR(255) PRIMARY KEY,
  "planId" VARCHAR(255) UNIQUE NOT NULL,
  "providerName" VARCHAR(50) NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  price INTEGER NOT NULL,
  credits INTEGER DEFAULT 0,
  status VARCHAR(50) DEFAULT 'active',
  interval VARCHAR(50) DEFAULT 'MONTHLY',
  "order" SERIAL,
  "createdAt" TIMESTAMP DEFAULT NOW(),
  "updatedAt" TIMESTAMP DEFAULT NOW(),
  UNIQUE("planId", "providerName")
);

-- Create Feature table
CREATE TABLE IF NOT EXISTS "Feature" (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) UNIQUE NOT NULL,
  "createdAt" TIMESTAMP DEFAULT NOW(),
  "updatedAt" TIMESTAMP DEFAULT NOW()
);

-- Create the many-to-many relationship table
CREATE TABLE IF NOT EXISTS "_FeatureToProduct" (
  "A" VARCHAR(255) NOT NULL REFERENCES "Product"(id) ON DELETE CASCADE,
  "B" VARCHAR(255) NOT NULL REFERENCES "Feature"(id) ON DELETE CASCADE,
  PRIMARY KEY ("A", "B")
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "_FeatureToProduct_A_index" ON "_FeatureToProduct"("A");
CREATE INDEX IF NOT EXISTS "_FeatureToProduct_B_index" ON "_FeatureToProduct"("B");

-- Insert some sample features for testing
INSERT INTO "Feature" (id, name, "createdAt", "updatedAt") 
VALUES 
  ('feature_1', 'API Access', NOW(), NOW()),
  ('feature_2', 'Premium Support', NOW(), NOW()),
  ('feature_3', 'Advanced Analytics', NOW(), NOW()),
  ('feature_4', 'Custom Integrations', NOW(), NOW()),
  ('feature_5', 'Priority Processing', NOW(), NOW())
ON CONFLICT (name) DO NOTHING;
